/**
 * 矩阵控制组件
 * 🎯 核心价值：纯UI控制组件，配置驱动，零业务逻辑
 * 📦 功能范围：模式切换、配置调整、状态显示
 * 🔄 架构设计：完全无状态组件，所有逻辑通过状态管理注入
 */

'use client';

import React, { memo, useCallback } from 'react';
import { useMatrixStore } from '@/core/matrix/MatrixStore';
import type { BusinessMode } from '@/core/matrix/MatrixTypes';

// ===== 组件属性 =====

interface ControlsProps {
  /** 自定义样式 */
  className?: string;
  style?: React.CSSProperties;
  
  /** 显示配置 */
  showModeSelector?: boolean;
  showStatusBar?: boolean;

  /** 事件回调 */
  onModeChange?: (mode: BusinessMode) => void;
  onReset?: () => void;
}

// ===== 模式选择器 =====

const ModeSelector: React.FC<{
  currentMode: BusinessMode;
  onModeChange: (mode: BusinessMode) => void;
}> = memo(({ currentMode, onModeChange }) => {
  const modes: Array<{ value: BusinessMode; label: string; shortcut: string }> = [
    { value: 'coordinate', label: '坐标模式', shortcut: 'Ctrl+1' },
    { value: 'color', label: '颜色模式', shortcut: 'Ctrl+2' },
    { value: 'value', label: '数值模式', shortcut: 'Ctrl+3' },
    { value: 'word', label: '词语模式', shortcut: 'Ctrl+4' },
  ];
  
  return (
    <div className="mode-selector">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        显示模式
      </label>
      <div className="grid grid-cols-2 gap-2">
        {modes.map(({ value, label, shortcut }) => (
          <button
            key={value}
            onClick={() => onModeChange(value)}
            className={`
              px-3 py-2 text-sm rounded-md border transition-colors
              ${currentMode === value
                ? 'bg-blue-500 text-white border-blue-500'
                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }
            `}
            title={shortcut}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  );
});

ModeSelector.displayName = 'ModeSelector';



// ===== 状态栏 =====

const StatusBar: React.FC<{
  mode: BusinessMode;
  selectedCount: number;
  totalCells: number;
}> = memo(({ mode, selectedCount, totalCells }) => {
  const modeLabels = {
    coordinate: '坐标',
    color: '颜色',
    value: '数值',
    word: '词语',
  };
  
  return (
    <div className="status-bar flex items-center justify-between p-2 bg-gray-50 border-t text-sm text-gray-600">
      <div className="flex items-center space-x-4">
        <span>模式: {modeLabels[mode]}</span>
        <span>已选择: {selectedCount}</span>
        <span>总计: {totalCells}</span>
      </div>
      <div className="text-xs text-gray-500">
        使用 Ctrl**** 快速切换模式
      </div>
    </div>
  );
});

StatusBar.displayName = 'StatusBar';

// ===== 主控制组件 =====

const ControlsComponent: React.FC<ControlsProps> = ({
  className = '',
  style,
  showModeSelector = true,
  showStatusBar = true,
  onModeChange,
  onReset,
}) => {
  const {
    data,
    config,
    setMode,
    initializeMatrix
  } = useMatrixStore();

  const mode = config.mode;
  const selectedCells = data.selectedCells;
  
  // 处理模式切换
  const handleModeChange = useCallback((newMode: BusinessMode) => {
    setMode(newMode);
    onModeChange?.(newMode);
  }, [setMode, onModeChange]);
  

  
  // 处理重置
  const handleReset = useCallback(() => {
    setMode('coordinate'); // 重置到默认模式
    initializeMatrix();
    onReset?.();
  }, [setMode, initializeMatrix, onReset]);
  
  return (
    <div className={`controls-container ${className}`} style={style}>
      <div className="p-4 space-y-6">
        {/* 模式选择器 */}
        {showModeSelector && (
          <ModeSelector
            currentMode={mode}
            onModeChange={handleModeChange}
          />
        )}
        

        
        {/* 操作按钮 */}
        <div className="space-y-2">
          <button
            onClick={handleReset}
            className="w-full px-4 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            重置矩阵
          </button>
        </div>
      </div>
      
      {/* 状态栏 */}
      {showStatusBar && (
        <StatusBar
          mode={mode}
          selectedCount={selectedCells.size}
          totalCells={1089}
        />
      )}
    </div>
  );
};

// ===== 性能优化 =====

const Controls = memo(ControlsComponent);

Controls.displayName = 'Controls';

export default Controls;
