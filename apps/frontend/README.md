# Cube1 Group - Next.js Frontend

🎯 **现代化网格数据可视化系统的前端实现**

基于Next.js 15.1.0 + React 18.3.1 + TypeScript 5.8.3的现代化前端架构，提供高性能的33×33网格渲染和交互功能。

## 🚀 快速开始

### 环境要求

- Node.js 18+
- pnpm 8.0.0+ (推荐)
- 现代浏览器支持

### 一键启动

```bash
# 进入前端目录
cd apps/frontend

# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev

# 访问应用
# http://localhost:4096
```

### 开发环境设置

```bash
# 完整开发环境设置
pnpm run dev:setup

# 启动开发服务器
pnpm run dev
```

## 🏗️ 项目结构

```
apps/frontend/
├── app/                    # Next.js App Router
│   ├── page.tsx           # 主应用页面
│   ├── layout.tsx         # 全局布局
│   ├── favicon.ico        # 网站图标
│   └── api/               # API路由
│       ├── health/        # 健康检查
│       ├── migration/     # 数据迁移
│       ├── performance/   # 性能监控
│       ├── projects/      # 项目管理
│       └── users/         # 用户管理
├── components/            # UI组件
│   ├── ControlPanel/      # 控制面板组件
│   └── ui/                # 基础UI组件
├── features/              # 业务功能模块
│   ├── grid-system/       # 网格系统
│   ├── style-management/  # 样式管理
│   └── shared/            # 共享模块合管理
│   └── shared/            # 共享模块
├── lib/                   # 工具库
│   ├── api/               # API工具
│   ├── hooks/             # 自定义Hooks
│   ├── providers/         # 上下文提供者
│   ├── types/             # 类型定义
│   ├── utils/             # 工具函数
│   └── validation/        # 数据验证
├── stores/                # Zustand状态管理
├── types/                 # TypeScript类型定义
│   ├── api/               # API类型
│   ├── config/            # 配置类型
│   ├── entities/          # 实体类型
│   └── store/             # Store类型
├── styles/                # 样式文件
│   └── globals.css        # 全局样式
├── scripts/               # 构建和工具脚本
├── prisma/                # 数据库配置
│   ├── schema.prisma      # 数据库模式
│   ├── seed.ts            # 数据种子
│   └── dev.db             # 开发数据库
└── public/                # 静态资源
    ├── favicon.svg        # SVG图标
    └── images/            # 图片资源
```

## 🛠️ 技术栈

### 核心框架
- **Next.js 15.1.0** - React全栈框架，App Router
- **React 18.3.1** - UI库，组件化开发
- **TypeScript 5.8.3** - 严格类型安全

### UI和样式
- **Tailwind CSS 3.4.17** - 原子化CSS框架
- **Radix UI** - 无障碍UI组件库（多个组件）
- **Lucide React 0.525.0** - 现代图标库
- **Heroicons React 2.2.0** - 英雄图标库
- **Class Variance Authority 0.7.0** - 组件变体管理
- **tailwind-merge 3.3.1** - 样式合并工具
- **tailwind-variants 1.0.0** - 样式变体工具
- **tailwindcss-animate 1.0.7** - 动画工具

### 状态管理
- **Zustand 5.0.6** - 轻量级状态管理
- **SWR 2.3.4** - 数据获取
- **Immer 10.1.1** - 不可变状态
- **React Hot Toast 2.5.2** - 通知组件

### 开发工具
- **Vitest 3.2.4** - 单元测试框架
- **Playwright 1.54.0** - E2E测试框架
- **ESLint 8 + Prettier 3.3.3** - 代码质量工具
- **TypeScript 5.8.3** - 类型检查
- **TSX 4.20.3** - TypeScript执行器
- **JSCPD 4.0.5** - 代码重复检测

### 数据库和API
- **Prisma 6.11.1** - ORM和数据库工具
- **SQLite 5.1.7** - 开发数据库
- **PostgreSQL (pg 8.16.3)** - 生产数据库
- **Zod 3.25.74** - 数据验证
- **Node Fetch 2.7.0** - HTTP客户端
- **OpenAPI TypeScript Codegen** - API类型生成

### 性能和监控
- **Vercel Analytics 1.5.0** - 网站分析
- **Vercel Speed Insights 1.2.0** - 性能监控
- **Lighthouse CI** - 性能审计
- **Next.js Bundle Analyzer** - 构建分析

## 📦 可用命令

### 开发命令
```bash
pnpm run dev              # 启动开发服务器
pnpm run build            # 构建生产版本
pnpm run start            # 启动生产服务器
pnpm run lint             # 代码检查
pnpm run type-check       # TypeScript类型检查
```

### 测试命令
```bash
# 注意：测试框架已配置但测试脚本需要添加
# Vitest 3.2.4 - 单元测试框架
# Playwright 1.54.0 - E2E测试框架
# 测试目录配置在 playwright.config.ts 中
```

### 数据库命令
```bash
pnpm run db:generate      # 生成Prisma客户端
pnpm run db:push          # 同步数据库结构
pnpm run db:studio        # 打开数据库管理界面
pnpm run db:migrate       # 运行数据库迁移
pnpm run db:seed          # 运行数据种子
pnpm run db:reset         # 重置数据库
```

### 工具命令
```bash
pnpm run format           # 代码格式化
pnpm run format:check     # 检查代码格式
pnpm run quality:check    # 代码质量检查
pnpm run quality:report   # 生成质量报告
pnpm run demo             # 一键演示启动
pnpm run analyze          # 构建分析
pnpm run pre-commit       # 提交前检查
pnpm run ci               # CI检查
pnpm run cache:clear      # 清除缓存
pnpm run api:generate     # 生成API类型
pnpm run env:setup        # 环境设置
pnpm run env:dev          # 开发环境配置
```

## 🎯 核心功能

### 🖥️ 网格系统
- **33x33网格矩阵** - 1089个可交互单元格
- **高性能渲染** - React.memo优化
- **实时交互** - 悬停、选择、批量操作

### 🎨 颜色管理
- **8色彩系统** - 红、青、黄、紫、橙、绿、蓝、粉
- **4层级架构** - Level 1-4 层次化数据
- **动态样式** - 实时颜色切换

### 📊 数据管理
- **混合存储** - LocalStorage + API双重存储
- **版本控制** - 多版本保存和切换
- **数据迁移** - 本地到云端迁移

### 🛠️ 开发工具
- **内置调试面板** - Ctrl+Shift+D打开
- **性能监控** - 实时性能指标
- **API测试** - 集成API测试界面

## 🔧 开发指南

### 组件开发
- 使用函数组件 + Hooks
- 应用React.memo优化
- 遵循Radix UI设计原则
- 使用Tailwind CSS样式

### 状态管理
- 优先使用Zustand Store
- 避免过度使用useState
- 使用SWR处理服务器状态
- 应用Immer处理复杂状态

### 类型安全
- 100% TypeScript覆盖
- 严格模式配置
- 共享类型定义
- Zod运行时验证

### 测试策略
- **Vitest 3.2.4** - 单元测试框架（已配置）
- **Playwright 1.54.0** - E2E测试框架（已配置）
- 测试目录配置：`./tests/e2e`（需要创建）
- 建议添加测试脚本到package.json

## 📚 相关文档

- [Features架构说明](./features/README.md) - 业务功能模块架构
- [Playwright配置](./playwright.config.ts) - E2E测试配置
- [Prisma Schema](./prisma/schema.prisma) - 数据库模式
- [TypeScript配置](./tsconfig.json) - 类型配置
- [Tailwind配置](./tailwind.config.ts) - 样式配置

## 🚀 部署

### 开发环境
```bash
pnpm run dev:setup        # 环境设置
pnpm run dev:full         # 完整开发环境启动
pnpm run dev              # 启动开发服务器
pnpm run dev:clean        # 清除缓存后启动
```

### 生产环境
```bash
pnpm run build:prod       # 生产构建
pnpm run env:prod         # 生产环境配置
pnpm run deploy:vercel    # 部署到Vercel
pnpm run deploy:preview   # 预览部署
pnpm run deploy:production # 生产部署
```

## 🤝 贡献指南

1. 遵循代码规范和最佳实践
2. 编写测试覆盖新功能
3. 更新相关文档
4. 提交前运行质量检查

---

**维护者**: Augment Agent
**最后更新**: 2025年7月17日
**技术支持**: 查看项目根目录README.md获取完整信息
