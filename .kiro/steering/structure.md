---
inclusion: always
---

# 项目结构与架构指南

## 核心架构原则

### Monorepo 组织结构

- **始终**按功能组织，而非技术层级
- **始终**遵循层次结构：页面 → 功能 → UI 组件
- **绝不**在未通过共享模块的情况下创建跨功能依赖

### 功能模块结构

创建任何新功能时，**始终**使用此确切结构：

```text
features/[feature-name]/
├── components/     # 功能特定的 UI 组件
├── hooks/         # 功能业务逻辑钩子  
├── store/         # 功能状态管理
├── types/         # 功能 TypeScript 定义
├── utils/         # 功能工具函数
└── index.ts       # 仅桶导出
```

## 状态管理架构

### 存储分离规则

**始终**在这些特定存储之间分离关注点：

- `basicDataStore` → 网格数据、坐标、颜色映射、矩阵数据管理
- `styleStore` → UI 配置、主题、显示偏好、样式预设管理
- `dynamicStyleStore` → 计算样式、缓存计算、响应式样式、交互状态
- `gridConfigStore` → 网格配置、显示模式、灰色模式管理

### 何时创建新存储

- **创建新存储**：当功能有 >3 个相关状态片段时
- **扩展现有存储**：当添加 1-2 个相关属性时
- **绝不**在同一存储中混合 UI 状态和业务数据

## 组件架构模式

### 组件类型及使用时机

**容器组件**（业务逻辑）：

- 处理数据获取和状态管理
- 连接到存储和 API
- 将数据传递给展示组件

**展示组件**（仅显示）：

- 通过 props 接收所有数据
- 无直接存储访问
- 纯粹专注于渲染

**复合组件**（复杂 UI）：

- 用于多部分组件（模态框、下拉菜单、复杂表单）
- 当组件有 >3 个相关子组件时实现

### 性能模式

**始终应用这些模式：**

- 用 `React.memo` 包装所有组件
- 对任何 >10 行的计算使用 `useMemo`
- 对传递给子组件的事件处理器使用 `useCallback`
- 在单个操作中批量相关状态更新

## 文件组织规则

### 目录结构决策

**创建新目录时：**

- 功能目录使用 `kebab-case`（`grid-system`）
- 技术目录使用 `camelCase`（`components`、`hooks`）
- **绝不**将功能嵌套超过 2 层深度

**文件放置规则：**

- 被 >1 个功能使用的类型 → `lib/types/[domain].ts`
- 被 1 个功能使用的类型 → `features/[name]/types/`
- 被 >1 个功能使用的工具 → `lib/utils/`
- 被 >1 个功能使用的组件 → `components/ui/`

### 导入/导出策略

**模块导出：**

- **始终**从 `index.ts` 文件使用桶导出
- **始终**优先使用命名导出而非默认导出
- **绝不**创建循环依赖

**导入组织：**

```typescript
// 1. 外部库
import React from 'react'
import { useQuery } from '@tanstack/react-query'

// 2. 内部工具和类型
import type { GridConfig } from '@/lib/types'
import { cn } from '@/lib/utils'

// 3. 功能导入
import { useGridData } from '../hooks'
```

## 决策树

### 何时创建新功能模块

```text
此功能是否被 >1 个页面使用？
├─ 是 → 在 features/ 中创建
└─ 否 → 保留在页面特定的 components/ 中

此功能是否有 >3 个相关组件？
├─ 是 → 创建完整功能结构
└─ 否 → 创建简单组件目录
```

### 何时使用不同的存储模式

```text
此状态是否在 >2 个组件间共享？
├─ 是 → 使用 Zustand 存储
└─ 否 → 使用本地 useState

此状态是否持久化？
├─ 是 → 添加持久化中间件
└─ 否 → 使用仅内存存储

此状态是否从其他状态计算而来？
├─ 是 → 使用 dynamicStyleStore 模式
└─ 否 → 使用直接状态存储
```

## 关键架构规则

### 网格系统约束

- **始终**假设 33x33 网格（1089 个单元格）无虚拟化
- **始终**对坐标使用基于 0 的索引（0-32）
- **绝不**创建无法处理完整网格负载的组件

### 错误处理架构

- **始终**为功能模块实现错误边界
- **始终**为失败的组件提供回退 UI
- **绝不**让错误导致整个应用程序崩溃

### TypeScript 集成

- **始终**为组件 props 定义接口
- **始终**在所有配置中使用严格模式
- **始终**为异步操作实现适当的错误类型
