---
inclusion: always
---

# Cube1 Group - 产品指南

## 核心产品规格
- **网格系统**: 33x33 矩阵 (1089 个单元格) 零虚拟化渲染
- **颜色系统**: 8 种不同颜色类别 (红、青、黄、紫、橙、绿、蓝、粉)
- **数据层级**: 4 层分层数据结构 (Level 1-4)
- **存储策略**: 混合 LocalStorage + API 同步
- **性能目标**: 无虚拟化实时渲染

## 关键架构规则

### 网格系统要求
- 网格坐标必须使用基于0的索引 (33x33网格为0-32)
- 所有 1089 个单元格必须无虚拟化渲染
- 所有网格组件使用 React.memo 防止不必要的重新渲染
- 为网格渲染失败实现错误边界

### 状态管理 (Zustand)

- **basicDataStore**: 网格数据，单元格坐标，颜色映射，矩阵数据管理
- **styleStore**: UI配置，主题设置，显示偏好，样式预设管理
- **dynamicStyleStore**: 计算样式，缓存计算，响应式样式，交互状态
- **gridConfigStore**: 网格配置，显示模式，灰色模式管理
- 为关键数据使用持久化中间件 (网格状态，用户偏好)
- 避免属性钻取 - 使用存储进行跨组件通信

### 性能约束
- 用户交互防抖 (最少 100ms)
- 昂贵计算使用 useMemo/useCallback
- 批量状态更新防止渲染抖动
- 缓存计算值避免重复计算

### 数据验证规则
- 颜色值必须根据8色系统验证
- 层级数据 (1-4) 必须是带适当验证的整数
- 实现带回滚功能的乐观更新
- 所有API响应必须根据 TypeScript 模式验证

## 代码约定

### 命名标准
- 网格操作：`cellIndex`, `colorCategory`, `dataLevel`
- 组件：`GridCell`, `CellData`, `ColorSystem`, `DataLevel`
- 网格算法使用描述性变量名
- 复杂网格操作添加 JSDoc 注释

### 组件模式
- 将显示组件与业务逻辑分离
- 复杂UI遵循复合组件模式
- 为面向用户的失败实现适当的错误消息
- 所有属性使用一致的 TypeScript 接口

### API 集成

- 使用 @tanstack/react-query 进行数据获取并适当处理错误
- 为失败的API调用实现重试逻辑和缓存策略
- 本地缓存网格数据以支持离线功能
- 根据定义的模式验证所有响应

## 测试要求

- 满载 1089 个单元格的性能测试
- 跨组件的颜色系统完整性验证
- 数据持久化和同步场景
- 网格布局一致性的视觉回归测试